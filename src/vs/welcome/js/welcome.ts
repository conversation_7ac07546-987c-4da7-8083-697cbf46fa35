/// <reference path='../index.d.ts' />

(() => {
    // #region 轮播图相关逻辑
    let currentSlide: number = 0;
    let totalSlides: number = 5;

    const { isDev, isMacOS } = window.electronAPI.getSystemInfo();

    // 从localStorage加载保存的位置
    function loadSavedPosition() {
        if (isDev) {
            const saved = localStorage.getItem('welcomeCarouselPosition');
            if (saved !== null) {
                currentSlide = parseInt(saved, 10);
                if (
                    isNaN(currentSlide) ||
                    currentSlide < 0 ||
                    currentSlide >= totalSlides
                ) {
                    currentSlide = 0;
                }
            }
        }
    }

    // 保存当前位置到localStorage
    function savePosition() {
        if (isDev) {
            localStorage.setItem('welcomeCarouselPosition', currentSlide.toString());
        }
    }

    function updateSlidePosition() {
        const carousel = document.querySelector<HTMLElement>('.carousel-inner');
        if (!carousel) return;
        carousel.style.transform = `translateX(-${currentSlide * 20}%)`;

        // 更新导航点
        document.querySelectorAll('.dot').forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });

        // 保存位置
        savePosition();
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlidePosition();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateSlidePosition();
    }

    function goToSlide(index: number) {
        currentSlide = index;
        updateSlidePosition();
    }

    // 键盘和触摸事件
    function initCarouselEvents() {
        // 键盘导航
        document.addEventListener('keydown', e => {
            if (e.key === 'ArrowRight') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });

        // 触摸支持
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', e => {
            touchStartX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', e => {
            touchEndX = e.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    nextSlide();
                } else {
                    prevSlide();
                }
            }
        }
    }

    // 初始化轮播
    function initCarousel() {
        loadSavedPosition();

        // 动态生成导航点
        const dotsContainer = document.querySelector('.carousel-dots');
        if (dotsContainer) {
            dotsContainer.innerHTML = ''; // 清空现有内容
            for (let i = 0; i < totalSlides; i++) {
                const dot = document.createElement('div');
                dot.className = `dot ${i === currentSlide ? 'active' : ''}`;
                dot.onclick = () => goToSlide(i);
                dotsContainer.appendChild(dot);
            }
        }

        updateSlidePosition();
        initCarouselEvents();
    }

    // #endregion

    // #region 欢迎页面的主要逻辑
    function completeWelcome() {
        if (window.electronAPI) {
            window.electronAPI.completeWelcome();
        }
    }

    function skipWelcome() {
        completeWelcome();
    }

    // 主题选择功能
    function selectTheme(theme: string) {
        // 移除所有主题卡片的active状态
        document.querySelectorAll('.theme-wrapper').forEach(card => {
            card.classList.remove('active');
        });

        // 添加选中主题的active状态
        const selectedCard = document.querySelector(
            `.theme-wrapper[onclick*="${theme}"]`
        );
        if (selectedCard) {
            selectedCard.classList.add('active');
            // 给 html 增加 data-theme 属性
            document.documentElement.setAttribute('data-theme', theme);
        }

        // 调用electron API设置主题
        if (window.electronAPI) {
            window.electronAPI.setTheme(theme);
        }
    }

    // 导入配置功能
    async function importFrom(source: string) {
        const el = document.querySelector(
            `[data-click-id="import-${source.toLowerCase()}"]`
        );
        if (!el) {
            console.error(`Import button for ${source} not found`);
            return;
        }
        if (el.classList.contains('loading') || el.classList.contains('success')) {
            return;
        }

        // 获取所有导入按钮
        const allImportButtons = document.querySelectorAll('[data-click-id^="import-"]');

        // 禁用所有导入按钮
        allImportButtons.forEach(button => {
            button.classList.add('loading');
        });

        // 获取当前按钮的文案元素
        const titleElement = el.querySelector('.import-option-title');

        if (window.electronAPI) {
            try {
                await window.electronAPI.importConfig(source);
                // 停止loading状态，添加成功状态，修改按钮文案为导入成功
                el.classList.remove('loading');
                el.classList.add('success');
                if (titleElement) {
                    titleElement.textContent = 'Import Successful';
                }
                // 导入成功后自动进入下一页
                setTimeout(() => {
                    nextSlide();
                }, 1500); // 延迟1.5秒让用户看到成功消息
            } catch (error) {
                console.error('Import failed:', error);
                // 停止loading状态并修改按钮文案为导入失败
                if (titleElement) {
                    titleElement.textContent = 'Import Failed, Please Retry';
                }
                // 重新启用所有按钮
                allImportButtons.forEach(button => {
                    button.classList.remove('loading');
                });
            }
        }
    }

    // 命令行工具安装
    async function installCLI() {
        const button = document.getElementById('installButton');
        if (!button) {
            console.error('Install button not found');
            return;
        }

        if (
            button.classList.contains('loading') ||
            button.classList.contains('success')
        ) {
            return;
        }

        button.classList.add('loading');
        const spinner = button.querySelector('.install-title');
        if (!spinner) {
            console.error('Spinner not found');
            return;
        }
        spinner.textContent = 'Installing...';

        if (window.electronAPI) {
            try {
                await window.electronAPI.installCLI();
                button.classList.remove('loading');
                button.classList.add('success');
                spinner.textContent = 'Installation Successful';
                // 导入成功后自动进入下一页
                nextSlide();
            } catch (error) {
                button.classList.remove('loading');
                spinner.textContent = 'Installation Failed, Please Retry';
            }
        }
    }

    // 登录
    async function login() {
        const button = document.getElementById('loginButton');
        if (!button) {
            console.error('Login button not found');
            return;
        }

        if (
            button.classList.contains('loading') ||
            button.classList.contains('success')
        ) {
            return;
        }

        button.classList.add('loading');
        const spinner = button.querySelector('.login-title');
        if (!spinner) {
            console.error('Spinner not found');
            return;
        }
        spinner.textContent = 'Logging In...';

        if (window.electronAPI) {
            try {
                await window.electronAPI.login();
                button.classList.remove('loading');
                button.classList.add('success');
                spinner.textContent = 'Login Successful';
                // 完成
                completeWelcome();
            } catch (error) {
                button.classList.remove('loading');
                spinner.textContent = 'Login Failed, Please Retry';
            }
        }
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', async () => {

        // 如果不是 macos ，不展示 安装命令
        if (!isMacOS) {
            const cliEl = document.querySelector<HTMLDivElement>('.cli-tool');
            cliEl?.remove();
        }

        // 如果即没有安装 vscode 又没有安装 cursor ，则不展示导入配置的页面
        const isCodeInstalled = await window.electronAPI.checkThirypartyIDEInstalled('code');
        const isCursorInstalled = await window.electronAPI.checkThirypartyIDEInstalled('cursor');
        if (!isCodeInstalled && !isCursorInstalled) {
            const importEl = document.querySelector<HTMLDivElement>('.import-settings');
            importEl?.remove();
        }

        // 如果没有安装 vscode 就把 import-from-code 的按钮去掉
        if (!isCodeInstalled) {
            const importFromCodeEl = document.querySelector<HTMLDivElement>('[data-click-id="import-code"]');
            importFromCodeEl?.remove();
        }
        // 如果没有安装 cursor 就把 import-from-cursor 的按钮去掉
        if (!isCursorInstalled) {
            const importFromCursorEl = document.querySelector<HTMLDivElement>('[data-click-id="import-cursor"]');
            importFromCursorEl?.remove();
        }

        const slideWrapper = document.querySelector<HTMLDivElement>('.carousel-inner');
        const slides = document.querySelectorAll<HTMLDivElement>('.carousel-slide');
        // 修正  totalSlides
        totalSlides = slides.length;
        if (slideWrapper) { slideWrapper.style.width = `${totalSlides * 100}%`; }
        // 修正 css
        const percent = 100 / totalSlides;
        slides.forEach(el => {
            el.style.width = `${percent}%`;
        });
        // 初始化轮播
        initCarousel();
    });

    // #endregion

    // exort
    window.func = {
        completeWelcome,
        skipWelcome,
        selectTheme,
        importFrom,
        installCLI,
        login,
        nextSlide,
        prevSlide,
        goToSlide,
    };
})();
