/** https://github.com/coreyhu/Urbanist#download-urbanist-v1303-on-google-fonts 字体 */
@font-face {
	font-family: "Urbanist";
	/* 自定义字体名称 */
	src: url("../assets/fonts/Urbanist[wght].ttf") format("truetype");
	font-weight: 100 900;
	/* 表示支持从100到900的字重范围 */
	font-style: normal;
	/* 字体风格，如normal、italic等 */
}

:root {
	--vscode-button-foreground: #ffffff;
	--vscode-textLink-foreground: #3794ff;
}

/** vscode token 对应表 https://docs.corp.kuaishou.com/d/home/<USER>/
:root[data-theme="Dark-Modern"] {
	--vscode-sideBar-background: #181818;
	--vscode-badge-foreground: #f8f8f8;
	--vscode-foreground: #cccccc;
	--vscode-descriptionForeground: #9d9d9d;
	--vscode-textLink-foreground: #4daafc;
	--vscode-button-background: #0078d4;
	--vscode-button-hoverBackground: #026ec1;
	--vscode-button-secondaryForeground: #cccccc;
	--vscode-button-secondaryBackground: #313131;
	--vscode-button-secondaryHoverBackground: #3c3c3c;
	--vscode-notebookScrollbarSlider-activeBackground: #bfbfbf66;
	--vscode-icon-foreground: #cccccc;
}

:root[data-theme="Light-Modern"] {
	--vscode-sideBar-background: #f3f3f3;
	--vscode-badge-foreground: #000000;
	--vscode-foreground: #000000;
	--vscode-descriptionForeground: #616161;
	--vscode-textLink-foreground: #005FB8;
	--vscode-button-background: #007acc;
	--vscode-button-hoverBackground: #0062a3;
	--vscode-button-secondaryForeground: #3B3B3B;
	--vscode-button-secondaryBackground: #E5E5E5;
	--vscode-button-secondaryHoverBackground: #e0e0e0;
	--vscode-notebookScrollbarSlider-activeBackground: #00000099;
	--vscode-icon-foreground: #3b3b3b;
}

:root[data-theme="Tomorrow-Night-Blue"] {
	--vscode-sideBar-background: #002451;
	--vscode-badge-foreground: #ffffff;
	--vscode-foreground: #ffffff;
	--vscode-descriptionForeground: #7285b7;
	--vscode-button-background: #7285b7;
	--vscode-button-hoverBackground: #8797c1;
	--vscode-button-secondaryForeground: #ffffff;
	--vscode-button-secondaryBackground: #2a4372;
	--vscode-button-secondaryHoverBackground: #3b5387;
	--vscode-notebookScrollbarSlider-activeBackground: #bfbfbf66;
	--vscode-icon-foreground: #cccccc;
}

/* 基础样式 */
html,
body {
	background-color: var(--vscode-sideBar-background);
	height: 100%;
	width: 100%;
	padding: 0;
	margin: 0;
}

.container {
	height: 100%;
	width: 100%;
	padding: 36px 0;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.container .carousel {
	width: 100%;
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.container .carousel-dots {
	width: 100%;
	height: 22px;
}

.content-frame .main-title {
	color: var(--vscode-badge-foreground);
	font-size: 48px;
	font-family: "Urbanist";
	font-weight: 800;
	text-align: center;
	vertical-align: middle;
	height: 72px;
}

.content-frame .sub-title {
	color: var(--vscode-foreground);
	font-size: 36px;
	font-family: "Urbanist";
	font-weight: 800;
	text-align: center;
	vertical-align: middle;
	height: 54px;
}

.content-frame * + .sub-title {
	margin-top: 24px;
}

.content-frame .desc-title {
	margin-top: 12px;
	color: var(--vscode-descriptionForeground);
	font-size: 18px;
	font-family: "Urbanist";
	font-weight: 300;
	text-align: center;
	vertical-align: middle;
}

.control-frame {
	margin-top: 48px;
}

.control-frame .primary-button {
	width: 400px;
	height: 56px;
	position: relative;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	gap: 10px;
	color: var(--vscode-button-foreground);
	border-radius: 8px;
	background-color: var(--vscode-button-background);
	font-size: 18px;
	font-weight: 500;
	letter-spacing: 0px;
	text-align: left;
	vertical-align: middle;
	cursor: pointer;
}

.control-frame .primary-button:not(.loading):hover {
	background-color: var(--vscode-button-hoverBackground);
}

.control-frame {
	margin-top: 48px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 16px;
}

.control-frame .secondary-button {
	background-color: var(--vscode-button-secondaryBackground);
	color: var(--vscode-foreground);
	width: 400px;
	height: 48px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 8px;
	cursor: pointer;
	gap: 12px;
	box-sizing: border-box;
	vertical-align: middle;
	font-weight: 500;
}

.control-frame .secondary-button:not(.loading):hover {
	background-color: var(--vscode-button-secondaryHoverBackground);
}

.control-frame .secondary-button .icon {
	width: 22px;
	height: 22px;
}

.control-frame .secondary-button .icon-cursor {
	background: url("../assets/icons/cursor.svg") no-repeat center center;
}

.control-frame .secondary-button .icon-vscode {
	background: url("../assets/icons/vscode.svg") no-repeat center center;
}

.control-frame .link-button {
	color: var(--vscode-descriptionForeground);
	cursor: pointer;
	font-size: 16px;
}

.control-frame .link-button:hover {
	color: var(--vscode-foreground);
}

.carousel-slide.import-settings .desc-title {
	width: 516px;
}

.content-frame .theme-cards {
	margin-top: 32px;
	display: flex;
	align-items: center;
	gap: 24px;
}

.theme-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.theme-card {
	width: 200px;
	height: 120px;
	border-radius: 13px;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.2s ease;
	border: 4px solid transparent;
}

.theme-card img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.theme-name {
	font-size: 16px;
	font-weight: 500;
	color: var(--vscode-foreground);
	margin-top: 8px;
	text-align: center;
}

.theme-card:hover {
	transform: translateY(-2px);
}

.theme-wrapper.active .theme-card {
	border-color: var(--vscode-textLink-foreground);
}

.theme-wrapper.active .theme-name {
	color: var(--vscode-textLink-foreground);
}

/* 加载动画 */
.loading-spinner {
	width: 22px;
	height: 22px;
	border: 3px solid rgba(255, 255, 255, 0.2);
	border-top-color: white;
	box-sizing: border-box;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	display: none;
}

.loading .loading-spinner {
	display: block;
}

.loading.secondary-button,
.loading.primary-button,
.success.secondary-button,
.success.primary-button {
	cursor: not-allowed;
}

.import-settings .loading .icon {
	display: none;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
